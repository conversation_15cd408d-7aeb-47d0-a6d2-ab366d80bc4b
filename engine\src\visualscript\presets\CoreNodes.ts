/**
 * 核心节点实现
 * 包含流程控制、数据操作、异常处理等基础节点
 */
import { Node, SocketType } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { EventNode } from '../nodes/EventNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { ExecutionContext } from '../execution/ExecutionContext';
import { Graph } from '../graph/Graph';

/**
 * 开始节点 - 脚本执行的入口点
 */
export class OnStartNode extends EventNode {
  public static readonly TYPE = 'core/onStart';

  constructor(options: any) {
    super({
      ...options,
      type: OnStartNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 开始节点没有输入插槽
    // 添加输出插槽
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输出'
    });
  }

  protected executeImpl(): any {
    // 触发执行流
    this.triggerFlow('exec');
    return true;
  }

  public onStart(): void {
    // 脚本开始时自动执行
    this.execute();
  }
}

/**
 * 分支节点 - 条件判断
 */
export class BranchNode extends FlowNode {
  public static readonly TYPE = 'core/branch';

  constructor(options: any) {
    super({
      ...options,
      type: BranchNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '条件判断',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'true',
      type: SocketType.FLOW,
      description: '条件为真时执行'
    });
    this.addOutput({
      name: 'false',
      type: SocketType.FLOW,
      description: '条件为假时执行'
    });
  }

  protected executeImpl(): any {
    const condition = this.getInputValue('condition') || false;

    if (condition) {
      this.triggerFlow('true');
    } else {
      this.triggerFlow('false');
    }

    return condition;
  }
}

/**
 * 序列节点 - 顺序执行多个操作
 */
export class SequenceNode extends FlowNode {
  public static readonly TYPE = 'core/sequence';

  constructor(options: any) {
    super({
      ...options,
      type: SequenceNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });

    // 输出插槽 - 支持多个顺序输出
    this.addOutput({
      name: 'exec1',
      type: SocketType.FLOW,
      description: '第一个执行输出'
    });
    this.addOutput({
      name: 'exec2',
      type: SocketType.FLOW,
      description: '第二个执行输出'
    });
    this.addOutput({
      name: 'exec3',
      type: SocketType.FLOW,
      description: '第三个执行输出'
    });
    this.addOutput({
      name: 'exec4',
      type: SocketType.FLOW,
      description: '第四个执行输出'
    });
  }

  protected executeImpl(): any {
    // 按顺序触发所有输出
    this.triggerFlow('exec1');
    this.triggerFlow('exec2');
    this.triggerFlow('exec3');
    this.triggerFlow('exec4');
    return true;
  }
}

/**
 * For循环节点
 */
export class ForLoopNode extends FlowNode {
  public static readonly TYPE = 'core/forLoop';
  private currentIndex: number = 0;

  constructor(options: any) {
    super({
      ...options,
      type: ForLoopNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });
    this.addInput({
      name: 'startIndex',
      type: SocketType.DATA,
      dataType: 'number',
      description: '起始索引',
      defaultValue: 0
    });
    this.addInput({
      name: 'endIndex',
      type: SocketType.DATA,
      dataType: 'number',
      description: '结束索引',
      defaultValue: 10
    });

    // 输出插槽
    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      description: '循环体执行'
    });
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      description: '循环完成'
    });
    this.addOutput({
      name: 'index',
      type: SocketType.DATA,
      dataType: 'number',
      description: '当前索引'
    });
  }

  protected executeImpl(): any {
    const startIndex = this.getInputValue('startIndex') || 0;
    const endIndex = this.getInputValue('endIndex') || 10;

    for (this.currentIndex = startIndex; this.currentIndex < endIndex; this.currentIndex++) {
      // 输出当前索引
      this.setOutputValue('index', this.currentIndex);

      // 执行循环体
      this.triggerFlow('loopBody');
    }

    // 循环完成
    this.triggerFlow('completed');
    return true;
  }
}

/**
 * While循环节点
 */
export class WhileLoopNode extends FlowNode {
  public static readonly TYPE = 'core/whileLoop';
  private maxIterations: number = 1000; // 防止无限循环

  constructor(options: any) {
    super({
      ...options,
      type: WhileLoopNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '循环条件',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'loopBody',
      type: SocketType.FLOW,
      description: '循环体执行'
    });
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      description: '循环完成'
    });
  }

  protected executeImpl(): any {
    let iterations = 0;

    while ((this.getInputValue('condition') || false) && iterations < this.maxIterations) {
      // 执行循环体
      this.triggerFlow('loopBody');
      iterations++;
    }

    // 循环完成
    this.triggerFlow('completed');
    return iterations;
  }
}

/**
 * 延迟节点
 */
export class DelayNode extends FlowNode {
  public static readonly TYPE = 'core/delay';

  constructor(options: any) {
    super({
      ...options,
      type: DelayNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });
    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      description: '延迟时间（毫秒）',
      defaultValue: 1000
    });

    // 输出插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      description: '延迟完成'
    });
  }

  protected executeImpl(): any {
    const duration = this.getInputValue('duration') || 1000; // 默认1秒

    setTimeout(() => {
      this.triggerFlow('completed');
    }, duration);

    return duration;
  }
}

/**
 * 设置变量节点
 */
export class SetVariableNode extends Node {
  public static readonly TYPE = 'core/setVariable';

  constructor(options: any) {
    super({
      ...options,
      type: SetVariableNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });
    this.addInput({
      name: 'variableName',
      type: SocketType.DATA,
      dataType: 'string',
      description: '变量名称'
    });
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      description: '变量值'
    });

    // 输出插槽
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输出'
    });
  }

  protected executeImpl(): any {
    const variableName = this.getInputValue('variableName') || '';
    const value = this.getInputValue('value');

    if (variableName) {
      // 在执行上下文中设置变量
      this.context.setVariable(variableName, value);
    }

    this.triggerFlow('exec');
    return value;
  }
}

/**
 * 获取变量节点
 */
export class GetVariableNode extends Node {
  public static readonly TYPE = 'core/getVariable';

  constructor(options: any) {
    super({
      ...options,
      type: GetVariableNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'variableName',
      type: SocketType.DATA,
      dataType: 'string',
      description: '变量名称'
    });

    // 输出插槽
    this.addOutput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      description: '变量值'
    });
  }

  protected executeImpl(): any {
    const variableName = this.getInputValue('variableName') || '';
    const value = this.context.getVariable(variableName);

    this.setOutputValue('value', value);
    return value;
  }
}

/**
 * 数组操作节点
 */
export class ArrayOperationNode extends Node {
  public static readonly TYPE = 'core/arrayOperation';

  constructor(options: any) {
    super({
      ...options,
      type: ArrayOperationNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      description: '数组',
      defaultValue: []
    });
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      description: '操作类型',
      defaultValue: 'get'
    });
    this.addInput({
      name: 'index',
      type: SocketType.DATA,
      dataType: 'number',
      description: '索引',
      defaultValue: 0
    });
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      description: '值'
    });

    // 输出插槽
    this.addOutput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输出'
    });
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'any',
      description: '操作结果'
    });
    this.addOutput({
      name: 'array',
      type: SocketType.DATA,
      dataType: 'array',
      description: '修改后的数组'
    });
  }

  protected executeImpl(): any {
    const array = this.getInputValue('array') || [];
    const operation = this.getInputValue('operation') || 'get';
    const index = this.getInputValue('index') || 0;
    const value = this.getInputValue('value');

    let result: any = null;

    switch (operation) {
      case 'push':
        result = array.push(value);
        break;
      case 'pop':
        result = array.pop();
        break;
      case 'get':
        result = array[index];
        break;
      case 'set':
        array[index] = value;
        result = value;
        break;
      case 'length':
        result = array.length;
        break;
      default:
        throw new Error(`不支持的数组操作: ${operation}`);
    }

    this.setOutputValue('result', result);
    this.setOutputValue('array', array);
    this.triggerFlow('exec');

    return result;
  }
}

/**
 * Try-Catch异常处理节点
 */
export class TryCatchNode extends FlowNode {
  public static readonly TYPE = 'core/tryCatch';

  constructor(options: any) {
    super({
      ...options,
      type: TryCatchNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'exec',
      type: SocketType.FLOW,
      description: '执行输入'
    });

    // 输出插槽
    this.addOutput({
      name: 'try',
      type: SocketType.FLOW,
      description: 'Try块执行'
    });
    this.addOutput({
      name: 'catch',
      type: SocketType.FLOW,
      description: 'Catch块执行'
    });
    this.addOutput({
      name: 'finally',
      type: SocketType.FLOW,
      description: 'Finally块执行'
    });
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'any',
      description: '错误信息'
    });
  }

  protected executeImpl(): any {
    try {
      // 执行try块
      this.triggerFlow('try');
    } catch (error) {
      // 执行catch块
      this.setOutputValue('error', error);
      this.triggerFlow('catch');
    } finally {
      // 执行finally块
      this.triggerFlow('finally');
    }

    return true;
  }
}

/**
 * 类型转换节点
 */
export class TypeConvertNode extends Node {
  public static readonly TYPE = 'core/typeConvert';

  constructor(options: any) {
    super({
      ...options,
      type: TypeConvertNode.TYPE
    });
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      description: '输入值'
    });
    this.addInput({
      name: 'targetType',
      type: SocketType.DATA,
      dataType: 'string',
      description: '目标类型',
      defaultValue: 'string'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'any',
      description: '转换结果'
    });
  }

  protected executeImpl(): any {
    const value = this.getInputValue('value');
    const targetType = this.getInputValue('targetType') || 'string';

    let result: any = null;

    switch (targetType) {
      case 'string':
        result = String(value);
        break;
      case 'number':
        result = Number(value);
        break;
      case 'boolean':
        result = Boolean(value);
        break;
      case 'array':
        result = Array.isArray(value) ? value : [value];
        break;
      case 'object':
        result = typeof value === 'object' ? value : { value };
        break;
      default:
        result = value;
    }

    this.setOutputValue('result', result);
    return result;
  }
}

/**
 * 注册核心节点到注册表
 */
export function registerCoreNodes(registry: NodeRegistry): void {
  registry.register(OnStartNode.TYPE, OnStartNode);
  registry.register(BranchNode.TYPE, BranchNode);
  registry.register(SequenceNode.TYPE, SequenceNode);
  registry.register(ForLoopNode.TYPE, ForLoopNode);
  registry.register(WhileLoopNode.TYPE, WhileLoopNode);
  registry.register(DelayNode.TYPE, DelayNode);
  registry.register(SetVariableNode.TYPE, SetVariableNode);
  registry.register(GetVariableNode.TYPE, GetVariableNode);
  registry.register(ArrayOperationNode.TYPE, ArrayOperationNode);
  registry.register(TryCatchNode.TYPE, TryCatchNode);
  registry.register(TypeConvertNode.TYPE, TypeConvertNode);
}
