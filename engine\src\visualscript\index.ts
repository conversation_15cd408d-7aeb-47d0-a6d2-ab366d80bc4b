/**
 * 视觉脚本系统模块
 * 导出所有视觉脚本系统相关的类和接口
 */

// 核心系统
export * from './VisualScriptSystem';
export * from './VisualScriptEngine';
export * from './VisualScriptComponent';

// 节点系统
export * from './nodes/Node';
export * from './nodes/FlowNode';
export * from './nodes/EventNode';
export * from './nodes/FunctionNode';
export * from './nodes/AsyncNode';
export * from './nodes/NodeRegistry';

// 图形系统
export * from './graph/Graph';
export * from './graph/GraphJSON';

// 执行系统
export * from './execution/Fiber';
export * from './execution/ExecutionContext';

// 事件系统
export * from './events/CustomEvent';

// 值类型系统
export * from './values/ValueTypeRegistry';
export * from './values/Variable';

// 预设节点 - 使用具名导出避免命名冲突
// CoreNodes - 重命名以避免与FlowNode中的同名类冲突
export {
  OnStartNode,
  BranchNode as CoreBranchNode,
  SequenceNode as CoreSequenceNode,
  ForLoopNode as CoreForLoopNode,
  WhileLoopNode as CoreWhileLoopNode,
  DelayNode as CoreDelayNode,
  SetVariableNode,
  GetVariableNode,
  ArrayOperationNode,
  TryCatchNode,
  TypeConvertNode,
  registerCoreNodes
} from './presets/CoreNodes';

// MathNodes - 导出所有数学节点
export * from './presets/MathNodes';

// DebugNodes - 导出所有调试节点
export * from './presets/DebugNodes';

// AINodes - 导出所有AI节点
export * from './presets/AINodes';

// 优化的节点注册系统
export {
  registerAllNodes,
  getNodeRegistrationStats,
  validateNodeRegistration,
  printNodeRegistrationReport,
  getNodeImplementationPriority,
  generateNodeImplementationPlan
} from './presets/OptimizedNodeRegistry';

// 导出节点注册配置类型
export type {
  NodeRegistrationConfig,
  NodeRegistrationStats,
  NodeRegistrationValidation
} from './presets/OptimizedNodeRegistry';

// 注意：以下节点文件尚未实现，暂时注释掉以避免导入错误
// 当这些文件实现后，可以取消注释相应的导出

// TODO: 实现以下节点文件后取消注释
// export * from './presets/EntityNodes';
// export * from './presets/PhysicsNodes';
// export * from './presets/AudioNodes';
// export * from './presets/NetworkNodes';
// export * from './presets/HTTPNodes';
// export * from './presets/JSONNodes';
// export * from './presets/DateTimeNodes';
// export * from './presets/UINodes';
// export * from './presets/AdvancedUINodes';
// export * from './presets/FileSystemNodes';
// export * from './presets/AdvancedFileSystemNodes';
// export * from './presets/ImageProcessingNodes';
// export * from './presets/AdvancedImageNodes';
// export * from './presets/DatabaseNodes';
// export * from './presets/CryptographyNodes';
// export * from './presets/DistributedExecutionNodes';
// export * from './presets/PerformanceMonitoringNodes';
// export * from './presets/CollaborationNodes';
// export * from './presets/AIAssistantNodes';
// export * from './presets/AdvancedDebuggingNodes';
// export * from './presets/PerformanceAnalysisNodes';
// export * from './presets/RenderingNodes';
// export * from './presets/SceneManagementNodes';
// export * from './presets/AssetManagementNodes';
// export * from './presets/AdvancedAnimationNodes';
// export * from './presets/AdvancedUILayoutNodes';
// export * from './presets/NetworkOptimizationNodes';
// export * from './presets/TerrainSystemNodes';
// export * from './presets/VegetationSystemNodes';
// export * from './presets/BlockchainSystemNodes';
// export * from './presets/FluidSimulationNodes';
// export * from './presets/AvatarCustomizationNodes';
// export * from './presets/AvatarPreviewNodes';
// export * from './presets/AvatarSaveNodes';
// export * from './presets/AvatarSceneNodes';
// export * from './presets/AvatarControlNodes';
// export * from './presets/AvatarUploadNodes';
// export * from './presets/SkeletonAnimationNodes';
// export * from './presets/AnimationNodes';
// export * from './presets/InputNodes';
// export * from './presets/LogicNodes';
// export * from './presets/TimeNodes';
